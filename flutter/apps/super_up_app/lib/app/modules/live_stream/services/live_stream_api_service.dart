// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';

import '../../../core/api_service/interceptors.dart';
import '../api/live_stream_api.dart';
import '../models/live_stream_model.dart';

class LiveStreamApiService {
  static LiveStreamApi? _liveStreamApi;

  LiveStreamApiService._();

  static LiveStreamApiService init() {
    _liveStreamApi = GetIt.I.get<LiveStreamApi>();
    return LiveStreamApiService._();
  }

  Future<LiveStreamModel> createLiveStream({
    required String title,
    String? description,
    bool? isPrivate,
    bool? requiresApproval,
    List<String>? allowedViewers,
    List<String>? tags,
    String? thumbnailUrl,
  }) async {
    final body = <String, dynamic>{
      'title': title,
      if (description != null) 'description': description,
      if (isPrivate != null) 'isPrivate': isPrivate,
      if (requiresApproval != null) 'requiresApproval': requiresApproval,
      if (allowedViewers != null) 'allowedViewers': allowedViewers,
      if (tags != null) 'tags': tags,
      if (thumbnailUrl != null) 'thumbnailUrl': thumbnailUrl,
    };

    final res = await _liveStreamApi!.createLiveStream(body);
    throwIfNotSuccess(res);

    final data = extractDataFromResponse(res);
    return LiveStreamModel.fromMap(data);
  }

  Future<LiveStreamModel> startLiveStream(String streamId) async {
    final res = await _liveStreamApi!.startLiveStream(streamId);
    throwIfNotSuccess(res);

    final data = extractDataFromResponse(res);
    return LiveStreamModel.fromMap(data);
  }

  Future<LiveStreamModel> endLiveStream(String streamId) async {
    final res = await _liveStreamApi!.endLiveStream(streamId);
    throwIfNotSuccess(res);

    final data = extractDataFromResponse(res);
    return LiveStreamModel.fromMap(data);
  }

  Future<Map<String, dynamic>> joinLiveStream(String streamId) async {
    final res = await _liveStreamApi!.joinLiveStream(streamId);
    throwIfNotSuccess(res);

    final data = extractDataFromResponse(res);
    return {
      'stream': LiveStreamModel.fromMap(data['stream']),
      'agoraToken': data['agoraToken'],
    };
  }

  Future<void> leaveLiveStream(String streamId) async {
    final res = await _liveStreamApi!.leaveLiveStream(streamId);
    throwIfNotSuccess(res);
  }

  Future<LiveStreamMessageModel> sendMessage({
    required String streamId,
    required String message,
    String? messageType,
    Map<String, dynamic>? giftData,
  }) async {
    // Check if API is initialized
    if (_liveStreamApi == null) {
      throw Exception('LiveStreamApi is not initialized');
    }

    // Validate streamId
    if (streamId.isEmpty) {
      throw Exception('StreamId cannot be empty');
    }

    // Validate message
    if (message.trim().isEmpty) {
      throw Exception('Message cannot be empty');
    }

    final body = <String, dynamic>{
      'message': message.trim(),
      if (messageType != null) 'messageType': messageType,
      if (giftData != null) 'giftData': giftData,
    };

    final res = await _liveStreamApi!.sendMessage(streamId, body);
    throwIfNotSuccess(res);

    final data = extractDataFromResponse(res);
    return LiveStreamMessageModel.fromMap(data);
  }

  Future<List<LiveStreamModel>> getLiveStreams({
    String? search,
    List<String>? tags,
    String? status,
    String? sortBy,
    String? sortOrder,
    int page = 1,
    int limit = 20,
  }) async {
    final queryParams = <String, dynamic>{
      'page': page,
      'limit': limit,
      if (search != null) 'search': search,
      if (tags != null) 'tags': tags,
      if (status != null) 'status': status,
      if (sortBy != null) 'sortBy': sortBy,
      if (sortOrder != null) 'sortOrder': sortOrder,
    };

    final res = await _liveStreamApi!.getLiveStreams(queryParams);
    throwIfNotSuccess(res);

    final data = extractDataFromResponse(res);
    final streams = data['streams'] as List;
    return streams.map((stream) => LiveStreamModel.fromMap(stream)).toList();
  }

  Future<LiveStreamModel> getStreamById(String streamId) async {
    final res = await _liveStreamApi!.getStreamById(streamId);
    throwIfNotSuccess(res);

    final data = extractDataFromResponse(res);
    return LiveStreamModel.fromMap(data);
  }

  Future<List<LiveStreamMessageModel>> getStreamMessages({
    required String streamId,
    int page = 1,
    int limit = 50,
  }) async {
    final res = await _liveStreamApi!.getStreamMessages(streamId, page, limit);
    throwIfNotSuccess(res);

    // Handle the response - it could be a direct list or wrapped in data
    final responseBody = res.body as Map<String, dynamic>;
    final List<dynamic> messages;

    if (responseBody.containsKey('data')) {
      final data = responseBody['data'];
      if (data is List) {
        messages = data;
      } else if (data is Map<String, dynamic> && data.containsKey('messages')) {
        messages = data['messages'] as List;
      } else {
        messages = [];
      }
    } else {
      messages = [];
    }

    return messages
        .map((message) =>
            LiveStreamMessageModel.fromMap(message as Map<String, dynamic>))
        .toList();
  }

  Future<List<LiveStreamParticipantModel>> getStreamParticipants(
      String streamId) async {
    final res = await _liveStreamApi!.getStreamParticipants(streamId);
    throwIfNotSuccess(res);

    // Handle the response - it could be a direct list or wrapped in data
    final responseBody = res.body as Map<String, dynamic>;
    final List<dynamic> participants;

    if (responseBody.containsKey('data')) {
      final data = responseBody['data'];
      if (data is List) {
        participants = data;
      } else if (data is Map<String, dynamic> &&
          data.containsKey('participants')) {
        participants = data['participants'] as List;
      } else {
        participants = [];
      }
    } else {
      participants = [];
    }

    return participants
        .map((participant) => LiveStreamParticipantModel.fromMap(
            participant as Map<String, dynamic>))
        .toList();
  }

  Future<void> updateLiveStream({
    required String streamId,
    String? title,
    String? description,
    bool? isPrivate,
    List<String>? allowedViewers,
    List<String>? tags,
  }) async {
    final body = <String, dynamic>{
      if (title != null) 'title': title,
      if (description != null) 'description': description,
      if (isPrivate != null) 'isPrivate': isPrivate,
      if (allowedViewers != null) 'allowedViewers': allowedViewers,
      if (tags != null) 'tags': tags,
    };

    final res = await _liveStreamApi!.updateLiveStream(streamId, body);
    throwIfNotSuccess(res);
  }

  Future<void> deleteLiveStream(String streamId) async {
    final res = await _liveStreamApi!.deleteLiveStream(streamId);
    throwIfNotSuccess(res);
  }

  Future<LiveStreamMessageModel> pinMessage({
    required String streamId,
    required String messageId,
  }) async {
    final res = await _liveStreamApi!.pinMessage(streamId, messageId);
    throwIfNotSuccess(res);

    final data = extractDataFromResponse(res);
    return LiveStreamMessageModel.fromMap(data);
  }

  Future<void> unpinMessage({
    required String streamId,
    required String messageId,
  }) async {
    final res = await _liveStreamApi!.unpinMessage(streamId, messageId);
    throwIfNotSuccess(res);
  }

  Future<LiveStreamMessageModel?> getPinnedMessage(String streamId) async {
    final res = await _liveStreamApi!.getPinnedMessage(streamId);
    throwIfNotSuccess(res);

    try {
      final data = extractDataFromResponse(res);
      return LiveStreamMessageModel.fromMap(data);
    } catch (e) {
      // If no pinned message exists, return null
      return null;
    }
  }

  Future<Map<String, dynamic>> removeParticipant({
    required String streamId,
    required String participantId,
    String? reason,
  }) async {
    final body = <String, dynamic>{
      'participantId': participantId,
      if (reason != null) 'reason': reason,
    };

    final res = await _liveStreamApi!.removeParticipant(streamId, body);
    throwIfNotSuccess(res);

    return extractDataFromResponse(res);
  }

  Future<Map<String, dynamic>> banParticipant({
    required String streamId,
    required String participantId,
    String? reason,
    String? duration,
  }) async {
    final body = <String, dynamic>{
      'participantId': participantId,
      if (reason != null) 'reason': reason,
      if (duration != null) 'duration': duration,
    };

    final res = await _liveStreamApi!.banParticipant(streamId, body);
    throwIfNotSuccess(res);

    return extractDataFromResponse(res);
  }

  Future<Map<String, dynamic>> likeStream(String streamId) async {
    final res = await _liveStreamApi!.likeStream(streamId);
    throwIfNotSuccess(res);

    return extractDataFromResponse(res);
  }

  Future<Map<String, dynamic>> getStreamLikes(String streamId) async {
    final res = await _liveStreamApi!.getStreamLikes(streamId);
    throwIfNotSuccess(res);

    return extractDataFromResponse(res);
  }

  Future<Map<String, dynamic>> requestJoinStream(String streamId) async {
    final res = await _liveStreamApi!.requestJoinStream(streamId);
    throwIfNotSuccess(res);
    return extractDataFromResponse(res);
  }

  Future<Map<String, dynamic>> respondToJoinRequest(
      String requestId, String action) async {
    final body = {'action': action};
    final res = await _liveStreamApi!.respondToJoinRequest(requestId, body);
    throwIfNotSuccess(res);
    return extractDataFromResponse(res);
  }

  Future<List<Map<String, dynamic>>> getJoinRequests(String streamId) async {
    try {
      final res = await _liveStreamApi!.getJoinRequests(streamId);
      throwIfNotSuccess(res);

      // Handle the response directly without using extractDataFromResponse
      final responseBody = res.body as Map<String, dynamic>;
      final data = responseBody['data'];

      if (kDebugMode) {
        print("Raw join requests data: $data");
        print("Data type: ${data.runtimeType}");
      }

      // Handle different response structures
      List<dynamic> list;
      if (data is List) {
        list = data;
      } else if (data is Map && data.containsKey('requests')) {
        list = data['requests'] as List<dynamic>;
      } else if (data is Map && data.containsKey('data')) {
        list = data['data'] as List<dynamic>;
      } else {
        if (kDebugMode) {
          print("Unexpected data structure: $data");
        }
        return [];
      }

      return list
          .map<Map<String, dynamic>>((e) {
            try {
              if (e is Map<String, dynamic>) {
                if (kDebugMode) {
                  print("Join request item: $e");
                }
                return e;
              } else if (e is Map) {
                final converted = Map<String, dynamic>.from(e);
                if (kDebugMode) {
                  print("Converted join request item: $converted");
                }
                return converted;
              } else {
                if (kDebugMode) {
                  print("Non-map element in list: $e (${e.runtimeType})");
                }
                // If it's not a Map, skip this item
                return <String, dynamic>{};
              }
            } catch (castError) {
              if (kDebugMode) {
                print("Error casting element: $e, error: $castError");
              }
              // Skip invalid items
              return <String, dynamic>{};
            }
          })
          .where((item) => item.isNotEmpty)
          .toList();
    } catch (e) {
      if (kDebugMode) {
        print("Error in getJoinRequests: $e");
      }
      return [];
    }
  }
}
